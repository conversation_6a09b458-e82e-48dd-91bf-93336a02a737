import { Button } from '@/components/ui/button'
import { FolderOpen, Plus, Palette, Code, Zap } from 'lucide-react'

interface WelcomeScreenProps {
    onCreateNewProject: () => void
    onOpenProject: () => void
}

export default function WelcomeScreen({ onCreateNewProject, onOpenProject }: WelcomeScreenProps) {
    return (
        <div className="flex items-center justify-center w-full h-full bg-background">
            <div className="max-w-2xl mx-auto text-center space-y-8 p-8">
                {/* Header Section */}
                <div className="space-y-4">
                    <div className="flex items-center justify-center space-x-3 mb-6">
                        <div className="p-3 bg-primary/10 rounded-xl">
                            <Code className="w-8 h-8 text-primary" />
                        </div>
                        <h1 className="text-4xl font-bold text-foreground">Website Builder</h1>
                    </div>
                    <p className="text-xl text-muted-foreground max-w-lg mx-auto leading-relaxed">
                        A powerful visual website builder that lets you create and edit websites with real-time preview and intuitive drag-and-drop functionality.
                    </p>
                </div>

                {/* Features Section */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-12">
                    <div className="flex flex-col items-center space-y-3 p-6 bg-card rounded-lg border">
                        <div className="p-3 bg-primary/10 rounded-lg">
                            <Palette className="w-6 h-6 text-primary" />
                        </div>
                        <h3 className="font-semibold text-card-foreground">Visual Editing</h3>
                        <p className="text-sm text-muted-foreground text-center">
                            Edit styles and content directly with an intuitive visual interface
                        </p>
                    </div>
                    <div className="flex flex-col items-center space-y-3 p-6 bg-card rounded-lg border">
                        <div className="p-3 bg-primary/10 rounded-lg">
                            <Zap className="w-6 h-6 text-primary" />
                        </div>
                        <h3 className="font-semibold text-card-foreground">Real-time Preview</h3>
                        <p className="text-sm text-muted-foreground text-center">
                            See your changes instantly as you build and customize your website
                        </p>
                    </div>
                    <div className="flex flex-col items-center space-y-3 p-6 bg-card rounded-lg border">
                        <div className="p-3 bg-primary/10 rounded-lg">
                            <Code className="w-6 h-6 text-primary" />
                        </div>
                        <h3 className="font-semibold text-card-foreground">Clean Code</h3>
                        <p className="text-sm text-muted-foreground text-center">
                            Generate clean, semantic HTML and CSS that you can export and use anywhere
                        </p>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-4">
                    <p className="text-lg font-medium text-foreground">Get started by creating a new project or opening an existing one</p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <Button 
                            onClick={onCreateNewProject}
                            size="lg"
                            className="w-full sm:w-auto min-w-[200px] h-12"
                        >
                            <Plus className="w-5 h-5 mr-2" />
                            Create New Project
                        </Button>
                        <Button 
                            onClick={onOpenProject}
                            variant="outline"
                            size="lg"
                            className="w-full sm:w-auto min-w-[200px] h-12"
                        >
                            <FolderOpen className="w-5 h-5 mr-2" />
                            Open Project
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}
