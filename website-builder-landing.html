<!DOCTYPE html>
<!--
========================================
WEBSITE BUILDER LANDING PAGE TEMPLATE SPECIFICATION - GLASSMORPHISM DESIGN
========================================

PURPOSE & CONCEPT:
- Landing page for a visual website builder application
- Showcases the tool's capabilities and features
- Promotes live development streams on YouTube
- Includes email signup for project updates
- Modern glassmorphism design with transparent glass-like elements

OVERALL DESIGN PHILOSOPHY:
- Glassmorphism aesthetic with frosted glass effects
- Layered transparent elements with backdrop blur
- Purple gradient color scheme with glass transparency
- Modern typography using Inter font family
- Abstract geometric background patterns
- Floating glass cards and panels
- Smooth animations and subtle shadows

COLOR PALETTE (GLASSMORPHISM):
- Primary Background: #0a0a0a (Deep Black)
- Glass Background: rgba(255, 255, 255, 0.1) with backdrop-blur
- Purple Primary: #6366f1 (Indigo-500)
- Purple Secondary: #8b5cf6 (Violet-500)
- Purple Light: #a855f7 (Purple-500)
- Text Primary: #ffffff (White)
- Text Secondary: #e2e8f0 (Slate-200)
- Text Muted: #94a3b8 (Slate-400)
- Glass Border: rgba(255, 255, 255, 0.2)

LAYOUT STRUCTURE:
1. GLASS HEADER - Floating transparent navigation
2. HERO SECTION - Large glass panels with abstract background
3. FEATURES SECTION - Floating glass cards in modern grid
4. ABOUT SECTION - Split glass panels with blur effects
5. FOOTER - Minimal glass footer

Modern glassmorphism design with elegant transparency effects.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Website Builder - Build Stunning Websites Without Code</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; color: #ffffff; 
            background: #0a0a0a;
            scroll-behavior: smooth; overflow-x: hidden;
        }
        
        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(168, 85, 247, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }
        
        @keyframes backgroundShift {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-20px) translateY(-10px); }
            50% { transform: translateX(20px) translateY(10px); }
            75% { transform: translateX(-10px) translateY(20px); }
        }
        
        .container { max-width: 1200px; margin: 0 auto; padding: 0 2rem; }
        
        /* Glass Header */
        .header {
            position: fixed; top: 1rem; left: 2rem; right: 2rem; z-index: 1000;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav { 
            display: flex; justify-content: space-between; align-items: center; 
            padding: 1rem 2rem;
        }
        
        .logo {
            font-size: 1.5rem; font-weight: 800; 
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        
        .nav-links { display: flex; gap: 2rem; list-style: none; align-items: center; }
        .nav-links a { 
            text-decoration: none; color: #e2e8f0; font-weight: 500; 
            transition: all 0.3s ease; padding: 0.5rem 1rem;
            border-radius: 10px;
        }
        .nav-links a:hover { 
            color: #ffffff; 
            background: rgba(255, 255, 255, 0.1);
        }
        
        .nav-cta { 
            background: rgba(99, 102, 241, 0.2); 
            color: #ffffff; padding: 0.75rem 1.5rem; border-radius: 12px; font-weight: 600;
            text-decoration: none; transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.3);
            backdrop-filter: blur(10px);
        }
        .nav-cta:hover { 
            background: rgba(99, 102, 241, 0.3); 
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }
        
        /* Hero Section */
        .hero {
            min-height: 100vh; display: flex; align-items: center; justify-content: center;
            padding: 8rem 0 4rem;
        }
        
        .hero-content {
            text-align: center; max-width: 1000px; margin: 0 auto; padding: 0 2rem;
        }
        
        .hero h1 {
            font-size: clamp(3rem, 8vw, 5rem); font-weight: 900; margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #6366f1 50%, #8b5cf6 100%);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
            line-height: 1.1;
        }
        
        .hero p {
            font-size: 1.25rem; margin-bottom: 3rem; color: #e2e8f0; 
            line-height: 1.7; max-width: 600px; margin-left: auto; margin-right: auto;
        }
        
        /* Glass Email Signup */
        .hero-signup {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 24px;
            padding: 2.5rem;
            margin: 3rem auto;
            max-width: 500px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        }
        
        .hero-signup h3 {
            font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;
            color: #ffffff; text-align: center;
        }
        
        .hero-signup p {
            font-size: 1rem; margin-bottom: 2rem; color: #e2e8f0; 
            text-align: center; opacity: 0.9;
        }
        
        .signup-form { display: flex; flex-direction: column; gap: 1rem; }
        
        .email-input {
            padding: 1rem 1.5rem; 
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px; 
            font-size: 1rem; color: #ffffff;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .email-input::placeholder { color: #94a3b8; }
        
        .email-input:focus {
            outline: none; 
            border-color: rgba(99, 102, 241, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }
        
        .hero-buttons { 
            display: flex; gap: 1rem; justify-content: center; 
            flex-wrap: wrap; margin-top: 2rem;
        }
        
        .btn {
            display: inline-flex; align-items: center; gap: 0.5rem;
            padding: 1rem 2rem; border-radius: 16px; text-decoration: none;
            font-weight: 600; font-size: 1rem; transition: all 0.3s ease; 
            border: none; cursor: pointer;
        }
        
        .btn-primary {
            background: rgba(99, 102, 241, 0.2); 
            color: #ffffff;
            border: 1px solid rgba(99, 102, 241, 0.3);
            backdrop-filter: blur(10px);
        }
        .btn-primary:hover { 
            background: rgba(99, 102, 241, 0.3);
            transform: translateY(-2px); 
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4); 
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.08); 
            color: #e2e8f0; 
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
        }
        .btn-secondary:hover { 
            background: rgba(255, 255, 255, 0.15); 
            transform: translateY(-2px);
            color: #ffffff;
        }
        
        /* Features Section */
        .features { 
            padding: 8rem 0; 
        }
        
        .section-title { 
            text-align: center; margin-bottom: 5rem; 
        }
        .section-title h2 { 
            font-size: clamp(2.5rem, 6vw, 3.5rem); font-weight: 800; margin-bottom: 1.5rem; 
            color: #ffffff; line-height: 1.2;
        }
        .section-title p { 
            font-size: 1.2rem; color: #e2e8f0; max-width: 600px; margin: 0 auto; 
        }
        
        .features-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); 
            gap: 2rem; 
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2.5rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.5), transparent);
        }
        
        .feature-card:hover { 
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(99, 102, 241, 0.3);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        }
        
        .feature-icon {
            width: 60px; height: 60px; 
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 16px; 
            display: flex; align-items: center; justify-content: center;
            margin-bottom: 1.5rem; font-size: 1.5rem;
            backdrop-filter: blur(10px);
        }
        
        .feature-card h3 { 
            font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem; color: #ffffff; 
        }
        
        .feature-card p { 
            color: #e2e8f0; line-height: 1.6; opacity: 0.9;
        }
        
        /* About Section */
        .about {
            padding: 8rem 0; 
        }
        
        .about-content {
            display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; 
            align-items: center; max-width: 1200px; margin: 0 auto; padding: 0 2rem;
        }
        
        .about-text h2 {
            font-size: clamp(2.5rem, 5vw, 3rem); font-weight: 800; margin-bottom: 2rem; 
            color: #ffffff; line-height: 1.2;
        }
        
        .about-text p {
            font-size: 1.1rem; color: #e2e8f0; margin-bottom: 1.5rem; line-height: 1.7;
        }
        
        .youtube-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 3rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .youtube-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ff0000, transparent);
        }
        
        .youtube-icon {
            width: 80px; height: 80px; 
            background: rgba(255, 0, 0, 0.2); 
            border: 1px solid rgba(255, 0, 0, 0.3);
            border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            margin: 0 auto 2rem; font-size: 2rem; color: #ff0000;
            backdrop-filter: blur(10px);
        }
        
        .youtube-card h3 { 
            color: #ffffff; margin-bottom: 1rem; font-size: 1.5rem; font-weight: 700;
        }
        .youtube-card p { 
            color: #e2e8f0; margin-bottom: 2rem; opacity: 0.9;
        }
        
        /* Footer */
        .footer {
            padding: 4rem 0 2rem;
            margin: 4rem 2rem 2rem;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 20px;
        }
        
        .footer-content {
            text-align: center; max-width: 1200px; margin: 0 auto; padding: 0 2rem;
        }
        
        .footer p { 
            color: #94a3b8; font-size: 0.9rem;
        }
        .footer a { 
            color: #8b5cf6; text-decoration: none; font-weight: 500;
            transition: color 0.3s ease;
        }
        .footer a:hover { color: #a855f7; }
        
        /* Responsive Design */
        @media (max-width: 1024px) {
            .about-content { grid-template-columns: 1fr; gap: 3rem; }
        }
        
        @media (max-width: 768px) {
            .header { left: 1rem; right: 1rem; }
            .nav { padding: 1rem 1.5rem; }
            .nav-links { display: none; }
            .hero { padding: 6rem 0 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .footer { margin: 2rem 1rem 1rem; }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="logo">WebBuilder</div>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#about">Live Streams</a></li>
                <li><a href="https://www.youtube.com/@ionelLupu_" target="_blank" class="nav-cta">Watch Live</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero">
            <div class="hero-content">
                <h1>Build Stunning Websites Without Code</h1>
                <p>Create beautiful, responsive websites with our intuitive visual editor. Drag, drop, and customize everything in real-time. No coding knowledge required.</p>
                
                <div class="hero-signup">
                    <h3>Get Early Access</h3>
                    <p>Be the first to know about new features and live stream schedules.</p>
                    <form class="signup-form" onsubmit="handleEmailSignup(event)">
                        <input 
                            type="email" 
                            class="email-input" 
                            placeholder="Enter your email address" 
                            required
                            name="email"
                        />
                        <button type="submit" class="btn btn-primary">
                            🔔 Get Updates
                        </button>
                    </form>
                </div>
                
                <div class="hero-buttons">
                    <a href="https://www.youtube.com/@ionelLupu_" target="_blank" class="btn btn-secondary">
                        📺 Watch Development Live
                    </a>
                </div>
            </div>
        </section>

        <section class="features" id="features">
            <div class="container">
                <div class="section-title">
                    <h2>Everything You Need to Build Amazing Websites</h2>
                    <p>Powerful features designed to make website creation simple, fast, and enjoyable for everyone.</p>
                </div>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <h3>Visual Drag & Drop Editor</h3>
                        <p>Intuitive interface that lets you build websites by simply dragging and dropping elements. See your changes instantly as you design.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <h3>Responsive Design</h3>
                        <p>All websites automatically adapt to any screen size. Your site will look perfect on desktop, tablet, and mobile devices.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>Real-Time Preview</h3>
                        <p>See exactly how your website will look as you build it. No need to switch between editor and preview modes.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>Professional Templates</h3>
                        <p>Start with beautiful, professionally designed templates. Customize colors, fonts, and layouts to match your brand.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <h3>Advanced Styling Controls</h3>
                        <p>Fine-tune every aspect of your design with powerful CSS controls. From margins to animations, you have full control.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💾</div>
                        <h3>Export & Download</h3>
                        <p>Download your completed website as clean HTML/CSS files. Host anywhere or integrate with your existing workflow.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="about" id="about">
            <div class="about-content">
                <div class="about-text">
                    <h2>Built Live on Stream</h2>
                    <p>This website builder is being developed completely in the open through live coding sessions. Watch the entire development process, learn web development techniques, and contribute ideas in real-time.</p>
                    <p>Join me every week as we add new features, fix bugs, and discuss architecture decisions. It's a unique opportunity to see how a real application is built from scratch.</p>
                    <p>All streams are saved so you can catch up on previous sessions and see how the project evolved over time.</p>
                </div>
                <div class="youtube-card">
                    <div class="youtube-icon">📺</div>
                    <h3>Live Development Streams</h3>
                    <p>Follow along as we build this tool together</p>
                    <a href="https://www.youtube.com/@ionelLupu_" target="_blank" class="btn btn-primary">
                        Subscribe to Channel
                    </a>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 Visual Website Builder. Built with passion during live streams. 
            <a href="https://www.youtube.com/@ionelLupu_" target="_blank">Watch the journey</a></p>
        </div>
    </footer>

    <script>
        function handleEmailSignup(event) {
            event.preventDefault();
            const email = event.target.email.value;
            
            if (!email || !email.includes('@')) {
                alert('Please enter a valid email address');
                return;
            }
            
            alert('Thank you for signing up! You\'ll receive updates about the website builder development.');
            event.target.reset();
        }
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.08)';
                header.style.borderColor = 'rgba(255, 255, 255, 0.15)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.05)';
                header.style.borderColor = 'rgba(255, 255, 255, 0.1)';
            }
        });
    </script>
</body>
</html> 