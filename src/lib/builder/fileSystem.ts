/**
 * File system utilities for the website builder
 */
import { clearFileData, getFileData, storeFileData } from '../db';
import {
  blankTemplate,
  personalWebsiteTemplate,
  businessLandingTemplate,
  onlineShopTemplate
} from '../templates/templates';

/**
 * Checks if the browser supports the File System Access API
 * @returns True if the browser supports the File System Access API
 */
export const isFileSystemAccessSupported = (): boolean => {
  return 'showOpenFilePicker' in window;
};

/**
 * Opens an HTML file using the File System Access API
 * @param onFileOpen - Callback function when a file is opened
 * @param onError - Callback function when an error occurs
 */
export const openProject = async (
  onFileOpen: (filename: string, content: string, handle: FileSystemFileHandle) => void,
  onError?: (error: any) => void
): Promise<void> => {
  if (!isFileSystemAccessSupported()) {
    alert('Your browser does not support the File System Access API. Please use a modern browser like Chrome or Edge.');
    return;
  }

  try {
    // Open file picker and get a file handle
    const [handle] = await window.showOpenFilePicker({
      types: [
        {
          description: 'HTML Files',
          accept: {
            'text/html': ['.html', '.htm']
          }
        }
      ],
      multiple: false
    });

    // Read the file
    const file = await handle.getFile();
    const content = await file.text();

    // Call the callback with the file information
    onFileOpen(handle.name, content, handle);

    // Save the file data for future use
    try {
      await storeFileData(handle);
    } catch (error) {
      console.error('Failed to save file data:', error);
      if (onError) onError(error);
    }
  } catch (error) {
    // User canceled the file picker or other error
    console.log('File picking was canceled or failed:', error);
    if (onError) onError(error);
  }
};

/**
 * Clears the saved file data
 * @param onClear - Callback function when the file data is cleared
 */
export const clearSavedFile = async (
  onClear: () => void
): Promise<void> => {
  await clearFileData();
  onClear();
};

/**
 * Loads saved file data from IndexedDB
 * @param onFileLoad - Callback function when a file is loaded
 * @param onError - Callback function when an error occurs
 */
export const loadSavedFileData = async (
  onFileLoad: (handle: FileSystemFileHandle | null, content?: string) => void,
  onError?: (error: any) => void
): Promise<void> => {
  try {
    // Try to get the file data from IndexedDB
    const fileHandle = await getFileData();

    if (fileHandle) {
      // If there's a handle, try to verify we still have permission to access it
      try {
        const options = { mode: 'read' as FileSystemPermissionMode };

        // Check if we already have permission
        const permission = await (fileHandle as any).queryPermission(options);

        if (permission === 'granted') {
          // Refresh the file content if we have permission
          try {
            const file = await fileHandle.getFile();
            const content = await file.text();
            onFileLoad(fileHandle, content);
          } catch (error) {
            console.warn('Could not refresh file content, using cached version:', error);
            onFileLoad(fileHandle);
          }
        } else {
          console.log('No permission to access file, using cached content');
          onFileLoad(fileHandle);
        }
      } catch (error) {
        console.warn('Error accessing file handle, but content was loaded from cache:', error);
        onFileLoad(fileHandle);
      }
    } else {
      onFileLoad(null);
    }
  } catch (error) {
    console.error('Error loading saved file:', error);
    // If there's an error, clear the saved data
    await clearFileData();
    if (onError) onError(error);
    onFileLoad(null);
  }
};

/**
 * Get template content by type
 * @param templateType - The type of template to get
 * @returns The HTML content for the specified template
 */
export const getTemplateByType = (templateType: 'blank' | 'personal' | 'business' | 'shop'): string => {
  switch (templateType) {
    case 'blank':
      return blankTemplate;
    case 'personal':
      return personalWebsiteTemplate;
    case 'business':
      return businessLandingTemplate;
    case 'shop':
      return onlineShopTemplate;
    default:
      return blankTemplate;
  }
};

/**
 * Creates a new project with the specified template
 * @param templateType - The type of template to use
 * @param onProjectCreate - Callback function when a new project is created
 * @param onError - Callback function when an error occurs
 */
export const createNewProject = async (
  templateType: 'blank' | 'personal' | 'business' | 'shop',
  onProjectCreate: (filename: string, content: string) => void,
  onError?: (error: any) => void
): Promise<void> => {
  try {
    const template = getTemplateByType(templateType);
    const filename = `${templateType}-project.html`;

    // Call the callback with the new project information using the selected template
    onProjectCreate(filename, template);
  } catch (error) {
    console.error('Error creating new project:', error);
    if (onError) onError(error);
  }
};

/**
 * Saves the current state of the iframe content back to the file
 * @param iframeRef - Reference to the iframe
 * @param fileHandle - The file handle to save to
 * @param onSuccess - Callback function when the file is saved successfully
 * @param onError - Callback function when an error occurs
 * @param selectedElement - Optional selected element to redraw overlay for
 * @param drawOverlayFn - Optional function to redraw overlay
 */
export const saveHtmlContent = async (
  iframeRef: HTMLIFrameElement,
  fileHandle: FileSystemFileHandle,
  onSuccess?: () => void,
  onError?: (error: any) => void,
  selectedElement?: HTMLElement,
  drawOverlayFn?: (element: HTMLElement) => void
): Promise<void> => {
  if (!iframeRef) {
    console.warn('Cannot save: iframe not available.');
    return;
  }

  const iframeDocument = iframeRef.contentDocument;
  if (!iframeDocument) {
    console.warn('Cannot save: iframe document not available.');
    return;
  }

  // Get the full HTML content from the iframe
  const updatedHtmlContent = iframeDocument.documentElement.outerHTML;

  try {
    // Create a writable stream
    const writable = await (fileHandle as any).createWritable();
    // Write the updated content
    await writable.write(updatedHtmlContent);
    // Close the file and write the changes
    await writable.close();

    console.log('File saved successfully!');

    // Redraw the overlay after styles are updated and saved
    if (drawOverlayFn && selectedElement) {
      drawOverlayFn(selectedElement);
    }

    if (onSuccess) onSuccess();
  } catch (error) {
    console.error('Error saving file:', error);
    // Handle potential permission errors or other issues
    if (error instanceof DOMException && error.name === 'NotAllowedError') {
      alert('Permission denied. Please grant access to the file to save changes.');
      // Optionally, try requesting permission again here
    } else {
      alert('An error occurred while saving the file.');
    }

    if (onError) onError(error);
  }
};
