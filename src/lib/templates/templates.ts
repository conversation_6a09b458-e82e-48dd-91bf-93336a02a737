/**
 * Website templates for different use cases
 */

export const blankTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blank Template</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background-color: #f8fafc;
            color: #334155;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 600px;
            text-align: center;
            background: white;
            padding: 60px 40px;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e293b;
            margin-bottom: 16px;
            font-size: 2rem;
            font-weight: 600;
        }
        p {
            color: #64748b;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Blank Template</h1>
        <p>This is a completely blank template. Start building your website from scratch by adding your own content and styling.</p>
    </div>
</body>
</html>`;

export const personalWebsiteTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>John Doe - Personal Website</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: #f7fafc;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4a5568;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #718096;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #4a5568;
        }
        
        /* Hero */
        .hero {
            padding: 4rem 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        /* About */
        .about {
            padding: 4rem 0;
            background: white;
        }
        
        .about h2 {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            color: #2d3748;
        }
        
        .about-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 3rem;
            align-items: center;
        }
        
        .profile-img {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            margin: 0 auto;
        }
        
        .about-text p {
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
            color: #4a5568;
        }
        
        /* Skills */
        .skills {
            padding: 4rem 0;
            background: #f7fafc;
        }
        
        .skills h2 {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            color: #2d3748;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .skill-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .skill-card h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #2d3748;
        }
        
        /* Footer */
        .footer {
            background: #2d3748;
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .hero h1 { font-size: 2rem; }
            .about-content { grid-template-columns: 1fr; text-align: center; }
            .nav-links { display: none; }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">John Doe</div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#skills">Skills</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero" id="home">
            <div class="container">
                <h1>Hi, I'm John Doe</h1>
                <p>Full-Stack Developer & Creative Problem Solver</p>
                <a href="#about" class="btn">Learn More About Me</a>
            </div>
        </section>

        <section class="about" id="about">
            <div class="container">
                <h2>About Me</h2>
                <div class="about-content">
                    <div class="profile-img">JD</div>
                    <div class="about-text">
                        <p>I'm a passionate full-stack developer with over 5 years of experience creating digital solutions that make a difference. I love turning complex problems into simple, beautiful designs.</p>
                        <p>When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or enjoying the great outdoors with my camera.</p>
                        <p>I believe in writing clean, maintainable code and creating user experiences that are both functional and delightful.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="skills" id="skills">
            <div class="container">
                <h2>My Skills</h2>
                <div class="skills-grid">
                    <div class="skill-card">
                        <h3>Frontend Development</h3>
                        <p>React, Vue.js, TypeScript, HTML5, CSS3, Tailwind CSS</p>
                    </div>
                    <div class="skill-card">
                        <h3>Backend Development</h3>
                        <p>Node.js, Python, PostgreSQL, MongoDB, REST APIs</p>
                    </div>
                    <div class="skill-card">
                        <h3>Tools & Technologies</h3>
                        <p>Git, Docker, AWS, Figma, VS Code, Linux</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer" id="contact">
        <div class="container">
            <p>&copy; 2024 John Doe. Let's build something amazing together!</p>
        </div>
    </footer>
</body>
</html>`;

export const businessLandingTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechSolutions - Transform Your Business</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; color: #1e293b;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .header {
            background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);
            padding: 1rem 0; position: sticky; top: 0; z-index: 100;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: 700; color: #4f46e5; }
        .nav-links { display: flex; gap: 2rem; list-style: none; }
        .nav-links a { text-decoration: none; color: #64748b; font-weight: 500; transition: color 0.3s ease; }
        .nav-links a:hover { color: #4f46e5; }
        .hero { padding: 6rem 0; text-align: center; color: white; }
        .hero h1 {
            font-size: 3.5rem; font-weight: 800; margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .hero p { font-size: 1.25rem; margin-bottom: 2.5rem; opacity: 0.9; max-width: 600px; margin-left: auto; margin-right: auto; }
        .cta-buttons { display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; }
        .btn {
            display: inline-block; padding: 1rem 2rem; border-radius: 12px; text-decoration: none;
            font-weight: 600; font-size: 1.1rem; transition: all 0.3s ease; border: none; cursor: pointer;
        }
        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white;
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 15px 35px rgba(79, 70, 229, 0.4); }
        .features { padding: 6rem 0; background: white; }
        .section-title { text-align: center; margin-bottom: 4rem; }
        .section-title h2 { font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1e293b; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .feature-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); padding: 2.5rem;
            border-radius: 20px; text-align: center; border: 1px solid #e2e8f0; transition: all 0.3s ease;
        }
        .feature-card:hover { transform: translateY(-5px); box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); }
        .feature-icon {
            width: 80px; height: 80px; background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border-radius: 20px; display: flex; align-items: center; justify-content: center;
            margin: 0 auto 1.5rem; font-size: 2rem; color: white;
        }
        .feature-card h3 { font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; color: #1e293b; }
        .footer { background: #1e293b; color: white; padding: 3rem 0; text-align: center; }
        @media (max-width: 768px) {
            .hero h1 { font-size: 2.5rem; }
            .nav-links { display: none; }
            .cta-buttons { flex-direction: column; align-items: center; }
            .btn { width: 100%; max-width: 300px; }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">TechSolutions</div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#services">Services</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>
    <main>
        <section class="hero">
            <div class="container">
                <h1>Transform Your Business</h1>
                <p>We help businesses grow with cutting-edge technology and innovative strategies that deliver real results.</p>
                <div class="cta-buttons">
                    <a href="#" class="btn btn-primary">Get Started Today</a>
                </div>
            </div>
        </section>
        <section class="features">
            <div class="container">
                <div class="section-title">
                    <h2>Why Choose TechSolutions</h2>
                </div>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🚀</div>
                        <h3>Fast & Reliable</h3>
                        <p>Lightning-fast performance and 99.9% uptime guarantee.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h3>Secure & Safe</h3>
                        <p>Enterprise-grade security with advanced encryption.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📈</div>
                        <h3>Scalable Growth</h3>
                        <p>Solutions that grow with your business needs.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 TechSolutions. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>`;

export const onlineShopTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StyleHub - Premium Fashion Store</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; color: #1f2937; background: #ffffff;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .header {
            background: white; padding: 1rem 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky; top: 0; z-index: 100;
        }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.8rem; font-weight: 700; color: #dc2626; }
        .nav-links { display: flex; gap: 2rem; list-style: none; }
        .nav-links a { text-decoration: none; color: #374151; font-weight: 500; transition: color 0.3s; }
        .nav-links a:hover { color: #dc2626; }
        .cart-icon { background: #dc2626; color: white; padding: 8px 12px; border-radius: 8px; font-weight: 600; }
        .hero {
            background: linear-gradient(135deg, #fef3f2 0%, #fee2e2 100%);
            padding: 4rem 0; text-align: center;
        }
        .hero h1 { font-size: 3rem; font-weight: 800; margin-bottom: 1rem; color: #1f2937; }
        .hero p { font-size: 1.2rem; margin-bottom: 2rem; color: #6b7280; }
        .btn {
            display: inline-block; padding: 12px 24px; background: #dc2626; color: white;
            text-decoration: none; border-radius: 8px; font-weight: 600; transition: all 0.3s;
        }
        .btn:hover { background: #b91c1c; transform: translateY(-2px); }
        .products { padding: 4rem 0; }
        .section-title { text-align: center; margin-bottom: 3rem; }
        .section-title h2 { font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937; }
        .products-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem; }
        .product-card {
            background: white; border-radius: 12px; overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: all 0.3s;
        }
        .product-card:hover { transform: translateY(-5px); box-shadow: 0 10px 25px rgba(0,0,0,0.15); }
        .product-image {
            height: 250px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            display: flex; align-items: center; justify-content: center; font-size: 3rem; color: #9ca3af;
        }
        .product-info { padding: 1.5rem; }
        .product-info h3 { font-size: 1.2rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937; }
        .product-price { font-size: 1.1rem; font-weight: 700; color: #dc2626; margin-bottom: 1rem; }
        .add-to-cart {
            width: 100%; padding: 10px; background: #1f2937; color: white; border: none;
            border-radius: 6px; font-weight: 600; cursor: pointer; transition: background 0.3s;
        }
        .add-to-cart:hover { background: #111827; }
        .features { padding: 4rem 0; background: #f9fafb; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; }
        .feature-item { text-align: center; padding: 2rem; }
        .feature-icon {
            width: 60px; height: 60px; background: #dc2626; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            margin: 0 auto 1rem; font-size: 1.5rem; color: white;
        }
        .footer { background: #1f2937; color: white; padding: 3rem 0; text-align: center; }
        @media (max-width: 768px) {
            .hero h1 { font-size: 2rem; }
            .nav-links { display: none; }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">StyleHub</div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#products">Products</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <div class="cart-icon">Cart (0)</div>
        </nav>
    </header>
    <main>
        <section class="hero">
            <div class="container">
                <h1>Premium Fashion Collection</h1>
                <p>Discover the latest trends in fashion with our curated collection of premium clothing and accessories.</p>
                <a href="#products" class="btn">Shop Now</a>
            </div>
        </section>
        <section class="products" id="products">
            <div class="container">
                <div class="section-title">
                    <h2>Featured Products</h2>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-image">👕</div>
                        <div class="product-info">
                            <h3>Premium Cotton T-Shirt</h3>
                            <div class="product-price">$29.99</div>
                            <button class="add-to-cart">Add to Cart</button>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-image">👖</div>
                        <div class="product-info">
                            <h3>Designer Jeans</h3>
                            <div class="product-price">$89.99</div>
                            <button class="add-to-cart">Add to Cart</button>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-image">👟</div>
                        <div class="product-info">
                            <h3>Sport Sneakers</h3>
                            <div class="product-price">$129.99</div>
                            <button class="add-to-cart">Add to Cart</button>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-image">🧥</div>
                        <div class="product-info">
                            <h3>Winter Jacket</h3>
                            <div class="product-price">$199.99</div>
                            <button class="add-to-cart">Add to Cart</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="features">
            <div class="container">
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">🚚</div>
                        <h3>Free Shipping</h3>
                        <p>Free delivery on orders over $50</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">↩️</div>
                        <h3>Easy Returns</h3>
                        <p>30-day return policy</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🔒</div>
                        <h3>Secure Payment</h3>
                        <p>Your payment information is safe</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 StyleHub. All rights reserved. Shop with confidence.</p>
        </div>
    </footer>
</body>
</html>`;


