import { HtmlNode } from '../lib/types'
import { Tree, TreeItem, TreeItemContent, TreeItemExpandButton } from '@/components/ui/tree'
import { Toggle, ToggleButtonGroup } from '@/components/ui/toggle'
import { useMemo, useState } from 'react'
import { Box, CircleX, Code, FormInput, List, Square, Type } from 'lucide-react'
import { Button } from '@/components/ui/button.tsx'

interface StructurePanelProps {
    filename: string;
    htmlStructure: HtmlNode | null;
    selectedElement?: HTMLElement;
    onClearFile: () => Promise<void>;
    onElementSelect?: (nodeId: string) => void;
}

export default function StructurePanel({
    filename,
    htmlStructure,
    selectedElement,
    onClearFile,
    onElementSelect
}: StructurePanelProps) {
    console.log('ssssss ->', )
    const [activeView, setActiveView] = useState<'structure' | 'other'>('structure')

    // Handle click on a tree item
    const handleTreeItemClick = (node: HtmlNode) => {
        if (onElementSelect) {
            onElementSelect(node.id)
        }
    }

    // Convert HtmlNode to a format compatible with the Tree component
    const renderTreeItems = (node: HtmlNode) => {
        return (
            <TreeItem
                key={node.id}
                id={node.id}
                textValue={node.id}
                className={selectedElement?.id === node.tagName ? 'bg-primary/10 text-primary' : ''}
            >
                <TreeItemContent>
                    {node.children.length > 0 && <TreeItemExpandButton/>}
                    <button
                        onClick={() => handleTreeItemClick(node)}
                        className="flex-1 text-left outline-none bg-transparent border-none cursor-pointer"
                    >
                        <span className="font-mono text-sm font-medium">{node.tagName}</span>
                        {/*{node.attributes.id && <span className="ml-1.5 text-purple-600 font-mono text-xs">#{node.attributes.id}</span>}*/}
                        {/*{node.attributes.class && <span className="ml-1.5 text-emerald-600 font-mono text-xs">.{node.attributes.class}</span>}*/}
                    </button>
                </TreeItemContent>

                {node.children.length > 0 && (
                    node.children.map(child => renderTreeItems(child))
                )}
            </TreeItem>
        )
    }

    const renderStructureView = useMemo(() => (
        <div className="w-full h-full overflow-y-auto  rounded-lg shadow-sm">
            <div className="flex justify-between items-center p-2 border-b border-border/50">
                <h3 className="m-0  font-semibold ">Structure</h3>
                <Button
                    onClick={onClearFile}
                    size="icon"
                    variant="ghost"
                >
                    <CircleX/>
                </Button>
            </div>
            {filename &&
                <p className="text-sm mb-4 px-2.5 py-1.5 bg-muted/30 rounded inline-block">File: {filename}</p>}

            <div className="py-1">
                {htmlStructure ? (
                    <Tree defaultExpandedKeys={[htmlStructure.id]}>
                        {renderTreeItems(htmlStructure)}
                    </Tree>
                ) : (
                    <div className="p-4 text-muted-foreground text-center italic bg-muted/30 rounded-md">
                        No HTML structure available
                    </div>
                )}
            </div>
        </div>
    ), [filename, htmlStructure, onClearFile, renderTreeItems])

    const renderComponentsView = useMemo(() => (
        <div className="w-full h-full overflow-y-auto  rounded-lg shadow-sm flex flex-col gap-2">
            <h3 className="m-0 text-base font-semibold p-2 border-b border-border/50">Components</h3>

            <div className="grid grid-cols-2 gap-4 p-2">
                {/* Button Component */}
                <div
                    className="flex flex-col items-center justify-center p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 cursor-move"
                    draggable={true}
                    onDragStart={(e) => {
                        e.dataTransfer.setData('component', 'button')
                    }}
                >
                    <Square className="h-6 w-6 text-muted-foreground mb-2"/>
                    <span className="text-xs">Button</span>
                </div>

                {/* Text Component */}
                <div
                    className="flex flex-col items-center justify-center p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 cursor-move"
                    draggable={true}
                    onDragStart={(e) => {
                        e.dataTransfer.setData('component', 'text')
                    }}
                >
                    <Type className="h-6 w-6 text-muted-foreground mb-2"/>
                    <span className="text-xs">Text</span>
                </div>

                {/* Input Component */}
                <div
                    className="flex flex-col items-center justify-center p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 cursor-move"
                    draggable={true}
                    onDragStart={(e) => {
                        e.dataTransfer.setData('component', 'input')
                    }}
                >
                    <FormInput className="h-6 w-6 text-muted-foreground mb-2"/>
                    <span className="text-xs">Input</span>
                </div>

                {/* Box Component */}
                <div
                    className="flex flex-col items-center justify-center p-3 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 cursor-move"
                    draggable={true}
                    onDragStart={(e) => {
                        e.dataTransfer.setData('component', 'box')
                    }}
                >
                    <Box className="h-6 w-6 text-muted-foreground mb-2"/>
                    <span className="text-xs">Box</span>
                </div>
            </div>
        </div>
    ), [])
    return (
        <div className="flex h-full">
            <div className="flex-shrink-0 border-r border-border/50 p-2">
                <ToggleButtonGroup
                    orientation="vertical"
                    selectionMode="single"
                    defaultSelectedKeys={['structure']}
                    className="flex flex-col gap-2"
                >
                    <Toggle
                        isSelected={activeView === 'structure'}
                        onChange={() => setActiveView('structure')}
                        aria-label="Structure View"
                    >
                        <Code className="h-4 w-4"/>
                    </Toggle>
                    <Toggle
                        isSelected={activeView === 'other'}
                        onChange={() => setActiveView('other')}
                        aria-label="Other View"
                    >
                        <List className="h-4 w-4"/>
                    </Toggle>
                </ToggleButtonGroup>
            </div>
            <div className="flex-grow">
                {activeView === 'structure' ? renderStructureView : renderComponentsView}
            </div>
        </div>
    )
};
